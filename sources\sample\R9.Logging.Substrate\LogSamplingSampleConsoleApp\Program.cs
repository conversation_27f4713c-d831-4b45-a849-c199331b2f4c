﻿// <copyright file="Program.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration;
using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler;
using OpenTelemetry.Logs;

namespace LogSamplingSampleConsoleApp
{
    /// <summary>
    /// A demo program to show how to use the sampling rules.
    /// </summary>
    internal class Program
    {
        private delegate void LogFunc(ILogger logger);

        private const string Category = "SamplingDemoCategory"; // If you want to change the Category, don't forget to modify the appsettings.json or ECS configuration!

        private static readonly string appsettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");

        /// <summary>
        /// This method shows how to use the RuleBasedSampler with option1/option2.
        /// 
        /// Onboard options:
        /// 1. Non-DI + Without using Substrate logging extension to connect to ECS 
        /// 2. Non-DI + Using Substrate logging extension to connect to ECS (Recommended)
        /// 3. DI + Without using Substrate logging extension to connect to ECS
        /// 4. DI + Using Substrate logging extension to connect to ECS (Recommended)
        /// 
        /// For DI scenario, most steps are similar, refer to the DISampleConsoleApp under sample.R9.Logging.Substrate for more details.
        /// </summary>
        static void Main()
        {
            // This method must be used with a ECS configuration section. See appsettings.json for details. 
            var configuration = ConfigurationHelper.LoadConfiguration(appsettingsPath);

            // Uncomment the following line and lines in appsettings if you don't want to use ECS. Also need to comment the ECS section.
            //var configuration = new ConfigurationBuilder().AddJsonFile(appsettingsPath, optional: false, reloadOnChange: true).Build();

            Console.WriteLine("The first test may take more time due to initialization.");

            TestTheRule(
                configuration,
                threadCount: 10,
                logsPerThread: 10000,
                expectedRate: 0.7,
                logFunc: (logger) => logger.LogInformation("Test message {Agent}", "Copilot"),
                desc: "Test the first rule: If Agent is in [Copilot, Deepseek], then the sample rate should be around 70%.");

            TestTheRule(
                configuration,
                threadCount: 10,
                logsPerThread: 1000,
                expectedRate: 0.3,
                logFunc: (logger) => logger.LogInformation("Test message {Severity} {UserId}", "1", $"{Guid.NewGuid()}"),
                desc: "Test the second rule: If Severity is 1, then the sample rate should be around 30%.");

            TestTheRule(
                configuration,
                threadCount: 10,
                logsPerThread: 10000,
                expectedRate: 1.0,
                logFunc: (logger) => logger.LogInformation("Test message without fields"),
                desc: "Test the missing rule: If there are no rules for the log, all logs should be sampled.");
        }

        /// <summary>
        /// Modify the parameters to test your rules and logs.
        /// 
        /// Tips:
        /// 1. Set the thread count to 1 to test the single thread scenario.
        /// 2. Expected rate should be aligned with your configurations. Don't forget to modify the appsettings.json or ECS configuration!
        /// </summary>
        private static void TestTheRule(IConfiguration configuration, int threadCount, int logsPerThread, double expectedRate, string desc, LogFunc logFunc)
        {
            var totalLogs = threadCount * logsPerThread;
            var expectedSampledCount = (int)(totalLogs * expectedRate);
            var threadResults = new ConcurrentDictionary<int, List<LogRecord>>();
            var sw = Stopwatch.StartNew();
            var tasks = Enumerable.Range(0, threadCount)
                .Select(threadId => Task.Run(() =>
                {
                    var threadExportedItems = new List<LogRecord>();
                    var threadLogger = CreateLogger(configuration, threadExportedItems, Category);

                    for (int i = 0; i < logsPerThread; i++)
                    {
                        logFunc(threadLogger);
                    }

                    threadResults[threadId] = threadExportedItems;
                }));
            Task.WhenAll(tasks).Wait();
            sw.Stop();

            var totalSampledCount = threadResults.Values.Sum(items => items.Count);
            var actualSampleRate = totalSampledCount / (double)totalLogs;
            var deviation = Math.Abs(actualSampleRate - expectedRate);

            Console.WriteLine(desc);
            Console.WriteLine($"Total logs: {totalLogs}");
            Console.WriteLine($"Expected sampled logs: {expectedSampledCount} \t Expected sample rate: {expectedRate * 100}%");
            Console.WriteLine($"Actual sampled logs: {totalSampledCount} \t Actual sample rate: {actualSampleRate * 100}%");
            Console.WriteLine($"Deviation: {deviation * 100}%");
            Console.WriteLine($"Elapsed time: {sw.ElapsedMilliseconds} ms");
            Console.WriteLine();
        }

        private static ILogger CreateLogger(IConfiguration configuration, List<LogRecord> exportedItems, string category)
        {
            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .SetMinimumLevel(LogLevel.Trace)
                    .AddRuleBasedSampler(configuration.GetSection("SubstrateLogging"))
                    .AddOpenTelemetry(options =>
                    {
                        options.AddInMemoryExporter(exportedItems);
                    });
            });
            return loggerFactory.CreateLogger(category);
        }
    }
}

