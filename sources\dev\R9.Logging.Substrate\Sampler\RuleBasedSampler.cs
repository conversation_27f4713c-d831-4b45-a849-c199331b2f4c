// <copyright file="RuleBasedSampler.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
#if NET8_0_OR_GREATER
using System.Collections.Frozen;
#endif

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
#pragma warning disable EXTEXP0003 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
    /// <summary>
    /// A sampler that uses ruleParamters to determine whether a log should be sampled.
    /// If sampled, keep the log. If not, discard the log.
    /// </summary>
    internal class RuleBasedSampler : LoggingSampler
#pragma warning restore EXTEXP0003 // Type is for evaluation purposes only and is subject to change or removal in future updates. Suppress this diagnostic to proceed.
    {
#if NET8_0_OR_GREATER
        private volatile FrozenDictionary<string, List<Rule>> categoryToRules = FrozenDictionary<string, List<Rule>>.Empty;
#else
        private volatile Dictionary<string, List<Rule>> categoryToRules = new Dictionary<string, List<Rule>>();
#endif

        /// <summary>
        /// Initializes a new instance of the <see cref="RuleBasedSampler"/> class.
        /// </summary>
        /// <param name="option">The options monitor for the sampler.</param>
        public RuleBasedSampler(IOptionsMonitor<RuleBasedSamplerOptions> option)
        {
            UpdateSamplerOption(option.CurrentValue);
            option.OnChange(UpdateSamplerOption);
        }

        /// <summary>
        /// Update the sampler options.
        /// </summary>
        /// <param name="option">The new sampler options.</param>
        private void UpdateSamplerOption(RuleBasedSamplerOptions option)
        {
            var newCategoryToRules = new Dictionary<string, List<Rule>>();
            foreach (var kvp in option.Sampler)
            {
                var category = kvp.Key;
                var ruleParamters = kvp.Value;
                var rules = ruleParamters.Select(para => new Rule(para.Constraints, para.StrategyParameter))
                                         .Where(rule => !rule.ShouldSkip())
                                         .ToList();
                if (rules.Count > 0)
                {
                    newCategoryToRules[category] = rules;
                }
            }

#if NET8_0_OR_GREATER
            categoryToRules = newCategoryToRules.ToFrozenDictionary();
#else
            categoryToRules = newCategoryToRules;
#endif
        }

        /// <summary>
        /// Makes a sampling decision for the provided <paramref name="logEntry"/>.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        /// <param name="logEntry">The log entry used to make the sampling decision for.</param>
        /// <typeparam name="TState">The type of the log entry state.</typeparam>
        /// <returns><see langword="true" /> if the log record should be sampled; otherwise, <see langword="false" />.</returns>
        public override bool ShouldSample<TState>(in LogEntry<TState> logEntry)
        {
            var category = logEntry.Category;
            if (categoryToRules.TryGetValue(category, out var rules))
            {
                foreach (var rule in rules)
                {
                    if (rule.TryMatch(logEntry))
                    {
                        return rule.ShouldSample(logEntry);
                    }
                }
            }
            return true; // No matched rules for this category. Keep the log.
        }
    }
}
